Write-Host "Starting AI Prompt Project..." -ForegroundColor Green
Write-Host ""

# Check if <PERSON><PERSON><PERSON> is running
Write-Host "Checking if <PERSON><PERSON><PERSON> is running..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:11434/api/tags" -TimeoutSec 5 -ErrorAction Stop
    Write-Host "✓ Ollama is running!" -ForegroundColor Green
} catch {
    Write-Host "⚠ WARNING: Ollama doesn't seem to be running on localhost:11434" -ForegroundColor Red
    Write-Host "Please make sure to:" -ForegroundColor Yellow
    Write-Host "1. Install Ollama from https://ollama.com/download" -ForegroundColor Yellow
    Write-Host "2. Run 'ollama serve' in another terminal" -ForegroundColor Yellow
    Write-Host "3. Run 'ollama pull llama3.1' to download the model" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Continuing anyway..." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Running the AI Prompt Generator..." -ForegroundColor Green
npm run auto-run

Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Cyan
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
