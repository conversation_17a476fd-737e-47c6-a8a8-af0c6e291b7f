import { generateWithOllama } from './ollamaService';
import * as readline from 'readline';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question: string): Promise<string> {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim());
    });
  });
}

async function interactiveMode() {
  console.log(`🤖 AI Prompt Generator - Interactive Mode`);
  console.log(`=========================================`);
  console.log('');

  try {
    // Ask for topic
    const topic = await askQuestion('What topic would you like a prompt about? ');
    if (!topic) {
      console.log('❌ Topic is required!');
      rl.close();
      return;
    }

    // Ask for category
    console.log('\nAvailable categories:');
    console.log('1. business   2. creative   3. academic   4. technical');
    console.log('5. marketing  6. storytelling  7. analysis  8. productivity');
    const categoryInput = await askQuestion('\nChoose category (1-8 or type name): ');
    
    const categoryMap: { [key: string]: string } = {
      '1': 'business', '2': 'creative', '3': 'academic', '4': 'technical',
      '5': 'marketing', '6': 'storytelling', '7': 'analysis', '8': 'productivity'
    };
    
    const category = categoryMap[categoryInput] || categoryInput || 'general';

    // Ask for tone
    console.log('\nAvailable tones:');
    console.log('1. formal     2. casual     3. creative   4. persuasive');
    console.log('5. analytical 6. humorous   7. inspirational  8. technical');
    const toneInput = await askQuestion('\nChoose tone (1-8 or type name): ');
    
    const toneMap: { [key: string]: string } = {
      '1': 'formal', '2': 'casual', '3': 'creative', '4': 'persuasive',
      '5': 'analytical', '6': 'humorous', '7': 'inspirational', '8': 'technical'
    };
    
    const tone = toneMap[toneInput] || toneInput || 'formal';

    console.log('');
    console.log(`Generating prompt about "${topic}" with category "${category}" and tone "${tone}"...`);
    console.log('');

    // Create system prompt
    let systemPrompt = `You are an AI assistant specialized in creating effective prompts about various topics.`;
    
    // Add category-specific context
    if (category === 'business') {
      systemPrompt += ' Focus on business and professional contexts.';
    } else if (category === 'creative') {
      systemPrompt += ' Focus on creative and artistic contexts.';
    } else if (category === 'academic') {
      systemPrompt += ' Focus on academic and educational contexts.';
    } else if (category === 'technical') {
      systemPrompt += ' Focus on technical and programming contexts.';
    }
    
    // Add tone-specific guidance
    if (tone === 'formal') {
      systemPrompt += ' Use formal and professional language.';
    } else if (tone === 'casual') {
      systemPrompt += ' Use casual and conversational language.';
    } else if (tone === 'creative') {
      systemPrompt += ' Use imaginative and original language.';
    }
    
    const userPrompt = `Create a concise and effective prompt about "${topic}".`;
    
    const result = await generateWithOllama(userPrompt, systemPrompt, 'llama3.1');
    
    console.log("✅ Generated Prompt:");
    console.log("==================");
    console.log(result);
    console.log('');
    console.log('🎉 Generation completed successfully!');
    
    // Ask if they want to generate another
    const again = await askQuestion('\nWould you like to generate another prompt? (y/n): ');
    if (again.toLowerCase() === 'y' || again.toLowerCase() === 'yes') {
      console.log('\n' + '='.repeat(50) + '\n');
      await interactiveMode(); // Run again
    }
    
  } catch (error) {
    console.error("❌ Error:", error);
    console.error("\n🔧 Troubleshooting:");
    console.error("1. Make sure Ollama is running: ollama serve");
    console.error("2. Test with: npm run check-setup");
  } finally {
    rl.close();
  }
}

interactiveMode();
