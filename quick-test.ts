import { generateWithOllama, getAvailableModels } from './ollamaService';

async function quickTest() {
  console.log('🧪 Quick Test of AI Prompt Generator');
  console.log('=====================================\n');

  try {
    // Test 1: Check available models
    console.log('1. Checking available models...');
    const models = await getAvailableModels();
    console.log(`✅ Found ${models.length} models:`);
    models.forEach(model => console.log(`   - ${model.name}`));
    console.log('');

    // Test 2: Simple prompt generation
    console.log('2. Testing prompt generation...');
    const systemPrompt = 'You are a helpful AI assistant that creates concise prompts.';
    const userPrompt = 'Create a simple prompt about "cooking pasta".';
    
    console.log('   Generating...');
    const result = await generateWithOllama(userPrompt, systemPrompt, 'llama3.1');
    
    console.log('✅ Generation successful!');
    console.log('\nGenerated Prompt:');
    console.log('----------------');
    console.log(result);
    console.log('\n🎉 Test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('\nTroubleshooting:');
    console.error('1. Make sure Ollama is running: ollama serve');
    console.error('2. Make sure you have a model: ollama pull llama3.1');
    console.error('3. Check if Ollama is accessible: curl http://localhost:11434/api/tags');
  }
}

quickTest();
