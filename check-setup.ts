import fetch from 'node-fetch';

const OLLAMA_API_URL = 'http://localhost:11434/api';

async function checkOllamaSetup() {
  console.log('🔍 Checking Ollama setup...\n');

  try {
    // Check if Ollama server is running
    console.log('1. Checking if Ollama server is running...');
    const response = await fetch(`${OLLAMA_API_URL}/tags`, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' }
    });

    if (!response.ok) {
      throw new Error(`Server responded with status: ${response.status}`);
    }

    const data = await response.json() as any;
    console.log('   ✅ Ollama server is running!');

    // Check if llama3.1 model is available
    console.log('2. Checking if llama3.1 model is available...');
    const models = data.models || [];
    const hasLlama31 = models.some((model: any) => 
      model.name && model.name.includes('llama3.1')
    );

    if (hasLlama31) {
      console.log('   ✅ llama3.1 model is available!');
      console.log('\n🎉 Setup is complete! You can now run the AI prompt generator.');
      console.log('\nTo run the project:');
      console.log('   - Double-click run.bat');
      console.log('   - Or run: npm run auto-run');
    } else {
      console.log('   ❌ llama3.1 model is not found.');
      console.log('\n📥 To install the model, run:');
      console.log('   ollama pull llama3.1');
    }

    console.log('\n📋 Available models:');
    if (models.length > 0) {
      models.forEach((model: any) => {
        console.log(`   - ${model.name}`);
      });
    } else {
      console.log('   No models found');
    }

  } catch (error) {
    console.log('   ❌ Ollama server is not running or not accessible');
    console.log('\n🚀 To set up Ollama:');
    console.log('1. Download and install Ollama from: https://ollama.com/download');
    console.log('2. Open a new terminal and run: ollama serve');
    console.log('3. In another terminal, run: ollama pull llama3.1');
    console.log('4. Then come back and run this check again');
    
    console.log('\n🔧 Error details:', error instanceof Error ? error.message : String(error));
  }
}

checkOllamaSetup();
