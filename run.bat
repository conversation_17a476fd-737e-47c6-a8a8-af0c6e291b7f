@echo off
echo Starting AI Prompt Project...
echo.

REM Check if <PERSON><PERSON><PERSON> is running
echo Checking if <PERSON><PERSON><PERSON> is running...
curl -s http://localhost:11434/api/tags >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: Ollama doesn't seem to be running on localhost:11434
    echo Please make sure to:
    echo 1. Install Ollama from https://ollama.com/download
    echo 2. Run 'ollama serve' in another terminal
    echo 3. Run 'ollama pull llama3.1' to download the model
    echo.
    echo Continuing anyway...
    echo.
)

echo Running the AI Prompt Generator...
npm run auto-run

echo.
echo Press any key to exit...
pause >nul
