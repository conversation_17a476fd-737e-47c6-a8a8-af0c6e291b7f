import { generateWithOllama } from './ollamaService';

// Random topics, categories, and tones
const topics = [
  "artificial intelligence", "cooking recipes", "space exploration", "sustainable energy",
  "digital marketing", "creative writing", "fitness training", "photography",
  "entrepreneurship", "meditation", "gardening", "music production",
  "web development", "climate change", "virtual reality", "cryptocurrency",
  "time management", "public speaking", "data science", "travel planning"
];

const categories = [
  "business", "creative", "academic", "technical", 
  "marketing", "storytelling", "analysis", "productivity"
];

const tones = [
  "formal", "casual", "creative", "persuasive",
  "analytical", "humorous", "inspirational", "technical"
];

function getRandomItem<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

async function generateRandomPrompt() {
  const topic = getRandomItem(topics);
  const category = getRandomItem(categories);
  const tone = getRandomItem(tones);

  console.log(`🎲 Random AI Prompt Generator`);
  console.log(`============================`);
  console.log(`Generating prompt about "${topic}" with category "${category}" and tone "${tone}"...`);
  console.log('');

  try {
    // Create system prompt
    let systemPrompt = `You are an AI assistant specialized in creating effective prompts about various topics.`;
    
    // Add category-specific context
    if (category === 'business') {
      systemPrompt += ' Focus on business and professional contexts.';
    } else if (category === 'creative') {
      systemPrompt += ' Focus on creative and artistic contexts.';
    } else if (category === 'academic') {
      systemPrompt += ' Focus on academic and educational contexts.';
    } else if (category === 'technical') {
      systemPrompt += ' Focus on technical and programming contexts.';
    }
    
    // Add tone-specific guidance
    if (tone === 'formal') {
      systemPrompt += ' Use formal and professional language.';
    } else if (tone === 'casual') {
      systemPrompt += ' Use casual and conversational language.';
    } else if (tone === 'creative') {
      systemPrompt += ' Use imaginative and original language.';
    }
    
    const userPrompt = `Create a concise and effective prompt about "${topic}".`;
    
    const result = await generateWithOllama(userPrompt, systemPrompt, 'llama3.1');
    
    console.log("✅ Generated Prompt:");
    console.log("==================");
    console.log(result);
    console.log('');
    console.log('🎉 Generation completed successfully!');
    console.log('');
    console.log('🎲 Run again to get a different random combination!');
    
  } catch (error) {
    console.error("❌ Error:", error);
    console.error("\n🔧 Troubleshooting:");
    console.error("1. Make sure Ollama is running: ollama serve");
    console.error("2. Test with: npm run check-setup");
  }
}

generateRandomPrompt();
