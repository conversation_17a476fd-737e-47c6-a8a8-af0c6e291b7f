{"name": "@types/jsonfile", "version": "6.1.4", "description": "TypeScript definitions for jsonfile", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/jsonfile", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "dbowring", "url": "https://github.com/dbowring"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/jsonfile"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "c4c437c24ccd22e0fd74368adceefc3c6ed726b74d1cc5188deeb5b0119b9523", "typeScriptVersion": "4.5"}