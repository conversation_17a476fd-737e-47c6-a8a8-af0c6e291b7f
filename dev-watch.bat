@echo off
echo Starting AI Prompt Project in WATCH mode...
echo This will automatically restart when you change the code.
echo.

REM Check if <PERSON><PERSON><PERSON> is running
echo Checking if <PERSON><PERSON><PERSON> is running...
curl -s http://localhost:11434/api/tags >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: <PERSON>llama doesn't seem to be running on localhost:11434
    echo Please make sure to:
    echo 1. Install Ollama from https://ollama.com/download
    echo 2. Run 'ollama serve' in another terminal
    echo 3. Run 'ollama pull llama3.1' to download the model
    echo.
)

echo Running in watch mode... Press Ctrl+C to stop
npm run watch
