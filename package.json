{"name": "ai-prompt-project", "version": "1.0.0", "description": "AI Prompt Generation Project using Ollama", "main": "index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node index.ts", "auto-run": "ts-node index.ts", "watch": "ts-node --watch index.ts", "check-setup": "ts-node check-setup.ts"}, "keywords": ["ai", "ollama", "prompt", "generation"], "author": "", "license": "ISC", "dependencies": {"typescript": "^5.0.0", "ts-node": "^10.9.1", "node-fetch": "^3.3.0"}, "devDependencies": {"@types/node": "^18.0.0"}}