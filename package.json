{"name": "ai-prompt-project", "version": "1.0.0", "description": "AI Prompt Generation Project using Ollama", "main": "index.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "ts-node index.ts", "auto-run": "ts-node index.ts", "watch": "ts-node --watch index.ts", "check-setup": "ts-node check-setup.ts", "server": "ts-node server.ts", "server-watch": "ts-node --watch server.ts"}, "keywords": ["ai", "ollama", "prompt", "generation"], "author": "", "license": "ISC", "dependencies": {"cors": "^2.8.5", "express": "^4.21.2", "fs-extra": "^11.1.1", "node-fetch": "^3.3.0", "ts-node": "^10.9.1", "typescript": "^5.0.0"}, "devDependencies": {"@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/fs-extra": "^11.0.1", "@types/node": "^18.0.0"}}