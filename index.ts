import { generateWithOllama } from './ollamaService';

// Example parameters
const topic: string = "artificial intelligence";
const category: string = "academic"; // Options: 'business', 'creative', 'academic', 'technical', 'marketing', 'storytelling', 'analysis', 'productivity'
const tone: string = "formal"; // Options: 'formal', 'casual', 'creative', 'persuasive', 'analytical', 'humorous', 'inspirational', 'technical'

async function main() {
  console.log(`🤖 AI Prompt Generator - Console Mode`);
  console.log(`=====================================`);
  console.log(`Generating prompt about "${topic}" with category "${category}" and tone "${tone}"...`);
  console.log('');

  try {
    // Create system prompt based on category and tone
    let systemPrompt = `You are an AI assistant specialized in creating effective prompts about various topics.`;

    // Add category-specific context
    if (category === 'business') {
      systemPrompt += ' Focus on business and professional contexts.';
    } else if (category === 'creative') {
      systemPrompt += ' Focus on creative and artistic contexts.';
    } else if (category === 'academic') {
      systemPrompt += ' Focus on academic and educational contexts.';
    } else if (category === 'technical') {
      systemPrompt += ' Focus on technical and programming contexts.';
    }

    // Add tone-specific guidance
    if (tone === 'formal') {
      systemPrompt += ' Use formal and professional language.';
    } else if (tone === 'casual') {
      systemPrompt += ' Use casual and conversational language.';
    } else if (tone === 'creative') {
      systemPrompt += ' Use imaginative and original language.';
    }

    const userPrompt = `Create a concise and effective prompt about "${topic}".`;

    const result = await generateWithOllama(userPrompt, systemPrompt, 'llama3.1');

    console.log("✅ Generated Prompt:");
    console.log("==================");
    console.log(result);
    console.log('');
    console.log('🎉 Generation completed successfully!');
    console.log('');
    console.log('💡 Tip: You can modify the topic, category, and tone in index.ts');
    console.log('🌐 For the full web interface, run: npm run server');

  } catch (error) {
    console.error("❌ Error:", error);

    // Check if Ollama is running
    console.error("\n🔧 Troubleshooting:");
    console.error("1. Make sure Ollama is installed and running on http://localhost:11434");
    console.error("2. You can install Ollama from: https://ollama.com/download");
    console.error("3. After installation, run 'ollama serve' and 'ollama pull llama3.1'");
    console.error("4. Test with: npm run check-setup");
  }
}

main();
