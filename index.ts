import { generateWithOllama } from './ollamaService.js';

// Example parameters
const topic = "artificial intelligence";
const category = "academic"; // Options: 'business', 'creative', 'academic'
const tone = "formal"; // Options: 'formal', 'casual', 'creative'

async function main() {
  console.log(`Generating prompt about "${topic}" with category "${category}" and tone "${tone}"...`);
  
  try {
    const result = await generateWithOllama(topic, category, tone);
    console.log("\nGenerated Prompt:");
    console.log("----------------");
    console.log(result);
  } catch (error) {
    console.error("Error:", error);
    
    // Check if <PERSON>lla<PERSON> is running
    console.error("\nMake sure Ollama is installed and running on http://localhost:11434");
    console.error("You can install Ollama from: https://ollama.com/download");
    console.error("After installation, run 'ollama serve' and 'ollama pull llama3.1'");
  }
}

main();
