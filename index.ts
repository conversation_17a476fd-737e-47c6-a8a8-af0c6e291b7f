import { generateWithOllama } from './ollamaService';
import * as readline from 'readline';

// Get command line arguments
const args = process.argv.slice(2);

// Check if user provided a topic as command line argument
const userTopic = args.join(' ');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question: string): Promise<string> {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim());
    });
  });
}

async function main() {
  console.log(`🤖 AI Prompt Generator - Interactive Mode`);
  console.log(`=========================================`);
  console.log('');

  try {
    let topic: string;

    // Check if topic was provided as command line argument
    if (userTopic) {
      topic = userTopic;
      console.log(`Using topic from command line: "${topic}"`);
    } else {
      // Ask for topic
      topic = await askQuestion('What topic would you like a prompt about? ');
      if (!topic) {
        console.log('❌ Topic is required!');
        rl.close();
        return;
      }
    }

    // Ask for category (optional)
    console.log('\nChoose a category (or press Enter for general):');
    console.log('business, creative, academic, technical, marketing, storytelling, analysis, productivity');
    const category = await askQuestion('Category: ') || 'general';

    // Ask for tone (optional)
    console.log('\nChoose a tone (or press Enter for formal):');
    console.log('formal, casual, creative, persuasive, analytical, humorous, inspirational, technical');
    const tone = await askQuestion('Tone: ') || 'formal';

    console.log('');
    console.log(`Generating prompt about "${topic}" with category "${category}" and tone "${tone}"...`);
    console.log('');

    // Create system prompt based on category and tone
    let systemPrompt = `You are an AI assistant specialized in creating effective prompts about various topics.`;

    // Add category-specific context
    if (category === 'business') {
      systemPrompt += ' Focus on business and professional contexts.';
    } else if (category === 'creative') {
      systemPrompt += ' Focus on creative and artistic contexts.';
    } else if (category === 'academic') {
      systemPrompt += ' Focus on academic and educational contexts.';
    } else if (category === 'technical') {
      systemPrompt += ' Focus on technical and programming contexts.';
    }

    // Add tone-specific guidance
    if (tone === 'formal') {
      systemPrompt += ' Use formal and professional language.';
    } else if (tone === 'casual') {
      systemPrompt += ' Use casual and conversational language.';
    } else if (tone === 'creative') {
      systemPrompt += ' Use imaginative and original language.';
    }

    const userPrompt = `Create a concise and effective prompt about "${topic}".`;

    const result = await generateWithOllama(userPrompt, systemPrompt, 'llama3.1');

    console.log("✅ Generated Prompt:");
    console.log("==================");
    console.log(result);
    console.log('');
    console.log('🎉 Generation completed successfully!');

    // Ask if they want to generate another
    const again = await askQuestion('\nWould you like to generate another prompt? (y/n): ');
    if (again.toLowerCase() === 'y' || again.toLowerCase() === 'yes') {
      console.log('\n' + '='.repeat(50) + '\n');
      // Restart the process
      process.argv = process.argv.slice(0, 2); // Clear command line args
      await main();
    }

  } catch (error) {
    console.error("❌ Error:", error);
    console.error("\n🔧 Troubleshooting:");
    console.error("1. Make sure Ollama is running: ollama serve");
    console.error("2. Test with: npm run check-setup");
  } finally {
    rl.close();
  }
}

main();
