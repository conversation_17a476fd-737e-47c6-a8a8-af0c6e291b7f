/**
 * Service for interacting with local Ollama API
 */

// For Node.js fetch in CommonJS
import fetch from 'node-fetch';

// Base URL for Ollama API - default local server
const OLLAMA_API_URL = 'http://localhost:11434/api';

export interface OllamaGenerateRequest {
  model: string;
  prompt: string;
  system?: string;
  template?: string;
  context?: number[];
  stream?: boolean;
  options?: {
    temperature?: number;
    top_p?: number;
    top_k?: number;
    num_predict?: number;
    num_ctx?: number;
  };
}

export interface OllamaGenerateResponse {
  model: string;
  response: string;
  done: boolean;
}

/**
 * Generate text using the Ollama API
 */
export const generateWithOllama = async (
  topic: string,
  category: string,
  tone: string
): Promise<string> => {
  try {
    // Create a system prompt based on category and tone
    let systemPrompt = `You are an AI assistant specialized in creating prompts about various topics.`;

    // Add category-specific context
    if (category === 'business') {
      systemPrompt += ' Focus on business and professional contexts.';
    } else if (category === 'creative') {
      systemPrompt += ' Focus on creative and artistic contexts.';
    } else if (category === 'academic') {
      systemPrompt += ' Focus on academic and educational contexts.';
    }

    // Add tone-specific guidance
    if (tone === 'formal') {
      systemPrompt += ' Use formal and professional language.';
    } else if (tone === 'casual') {
      systemPrompt += ' Use casual and conversational language.';
    } else if (tone === 'creative') {
      systemPrompt += ' Use imaginative and original language.';
    }

    // User prompt that includes the topic
    const prompt = `Create a concise and effective prompt about "${topic}".`;

    const response = await fetch(`${OLLAMA_API_URL}/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'llama3.1', // Using the llama3.1 model running on Ollama
        prompt,
        system: systemPrompt,
        stream: false, // Disable streaming for simpler response handling
        options: {
          temperature: 0.7,
          top_p: 0.9,
          top_k: 40
        }
      } as OllamaGenerateRequest),
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`Ollama API error: ${response.status} - ${errorData}`);
    }

    const data = await response.json() as OllamaGenerateResponse;
    return data.response;
  } catch (error) {
    console.error('Error generating with Ollama:', error);
    throw new Error(`Failed to generate with Ollama: ${error instanceof Error ? error.message : String(error)}`);
  }
};
