// Simple HTTP server using Node.js built-in modules
import * as http from 'http';
import * as fs from 'fs';
import * as path from 'path';
import * as url from 'url';
import { generateWithOllama, getAvailableModels } from './ollamaService';

const app = express();
const PORT = process.env.PORT || 3000;
const promptManager = new PromptManager();

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, 'public')));

// Serve static files from public directory
app.use(express.static('public'));

// API Routes

// Get available Ollama models
app.get('/api/models', async (req, res) => {
  try {
    const models = await getAvailableModels();
    res.json({ success: true, models });
  } catch (error) {
    res.status(500).json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to fetch models' 
    });
  }
});

// Get prompt templates and categories
app.get('/api/templates', (req, res) => {
  res.json({ 
    success: true, 
    templates: promptTemplates,
    categories,
    tones
  });
});

// Generate prompt
app.post('/api/generate', async (req, res) => {
  try {
    const { 
      topic, 
      templateId, 
      model = 'llama3.1', 
      tone = 'formal',
      customSystemPrompt,
      options = {}
    } = req.body;

    if (!topic) {
      return res.status(400).json({ success: false, error: 'Topic is required' });
    }

    let systemPrompt = '';
    let userPrompt = '';
    let templateUsed = 'custom';

    if (templateId && templateId !== 'custom') {
      const template = getTemplateById(templateId);
      if (!template) {
        return res.status(400).json({ success: false, error: 'Invalid template ID' });
      }
      
      systemPrompt = template.systemPrompt;
      userPrompt = template.userPromptTemplate.replace('{topic}', topic);
      templateUsed = template.name;
    } else {
      systemPrompt = customSystemPrompt || `You are an AI assistant specialized in creating effective prompts. Use a ${tone} tone.`;
      userPrompt = `Create a concise and effective prompt about "${topic}".`;
    }

    const generatedText = await generateWithOllama(userPrompt, systemPrompt, model, options);

    res.json({
      success: true,
      result: {
        topic,
        template: templateUsed,
        model,
        tone,
        prompt: userPrompt,
        systemPrompt,
        generatedText,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to generate prompt'
    });
  }
});

// Save prompt
app.post('/api/prompts', async (req, res) => {
  try {
    const promptData = req.body;
    const savedPrompt = await promptManager.savePrompt(promptData);
    res.json({ success: true, prompt: savedPrompt });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to save prompt'
    });
  }
});

// Get all prompts
app.get('/api/prompts', async (req, res) => {
  try {
    const prompts = await promptManager.getAllPrompts();
    res.json({ success: true, prompts });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch prompts'
    });
  }
});

// Search prompts
app.get('/api/prompts/search', async (req, res) => {
  try {
    const filters = req.query;
    const prompts = await promptManager.searchPrompts(filters as any);
    res.json({ success: true, prompts });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to search prompts'
    });
  }
});

// Get specific prompt
app.get('/api/prompts/:id', async (req, res) => {
  try {
    const prompt = await promptManager.getPrompt(req.params.id);
    if (!prompt) {
      return res.status(404).json({ success: false, error: 'Prompt not found' });
    }
    res.json({ success: true, prompt });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch prompt'
    });
  }
});

// Update prompt
app.put('/api/prompts/:id', async (req, res) => {
  try {
    const updatedPrompt = await promptManager.updatePrompt(req.params.id, req.body);
    if (!updatedPrompt) {
      return res.status(404).json({ success: false, error: 'Prompt not found' });
    }
    res.json({ success: true, prompt: updatedPrompt });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update prompt'
    });
  }
});

// Delete prompt
app.delete('/api/prompts/:id', async (req, res) => {
  try {
    const deleted = await promptManager.deletePrompt(req.params.id);
    if (!deleted) {
      return res.status(404).json({ success: false, error: 'Prompt not found' });
    }
    res.json({ success: true, message: 'Prompt deleted successfully' });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete prompt'
    });
  }
});

// Get statistics
app.get('/api/stats', async (req, res) => {
  try {
    const stats = await promptManager.getStats();
    res.json({ success: true, stats });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch statistics'
    });
  }
});

// Export prompts
app.get('/api/export', async (req, res) => {
  try {
    const format = req.query.format as 'json' | 'csv' || 'json';
    const exportData = await promptManager.exportPrompts(format);
    
    const filename = `prompts_export_${new Date().toISOString().split('T')[0]}.${format}`;
    const contentType = format === 'json' ? 'application/json' : 'text/csv';
    
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Type', contentType);
    res.send(exportData);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to export prompts'
    });
  }
});

// Serve the main HTML file for all non-API routes
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 AI Prompt Generator Server running on http://localhost:${PORT}`);
  console.log(`📝 Web interface available at http://localhost:${PORT}`);
  console.log(`🔧 API endpoints available at http://localhost:${PORT}/api`);
});
