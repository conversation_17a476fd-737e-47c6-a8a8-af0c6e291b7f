# AI Prompt Generation Project

This project uses Ollama to generate AI prompts based on a given topic, category, and tone.

## Prerequisites

1. **Node.js and npm**: Make sure you have Node.js (v16 or later) installed.
2. **Ollama**: You need to have Ollama installed and running locally.

## Installation

### 1. Install Ollama

Download and install Ollama from [https://ollama.com/download](https://ollama.com/download)

After installation, run Ollama:

```bash
ollama serve
```

### 2. Pull the Llama 3.1 model:

```bash
ollama pull llama3.1
```

### 3. Install project dependencies:

```bash
npm install
```

## Running the Project

```bash
npm run dev
```

This will generate a prompt based on the parameters defined in `index.ts`.

## Customizing

Edit the parameters in `index.ts` to change:
- The topic
- The category (business, creative, academic)
- The tone (formal, casual, creative)

## Building for Production

```bash
npm run build
npm start
```

## Troubleshooting

If you encounter errors:

1. Make sure <PERSON><PERSON><PERSON> is running (`ollama serve`)
2. Verify that you've pulled the llama3.1 model (`ollama pull llama3.1`)
3. Check that all dependencies are installed (`npm install`)
